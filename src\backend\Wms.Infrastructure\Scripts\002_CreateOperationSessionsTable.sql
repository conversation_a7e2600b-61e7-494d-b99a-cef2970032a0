-- <PERSON><PERSON>ie tabeli operations_sessions dla uniwersalnych sesji operacyjnych
-- Ta tabela zastępuje dedykowane tabele sesji dla poszczególnych modułów

-- <PERSON><PERSON><PERSON> obecną tabelę jeśli została częściowo utworzona
DROP TABLE IF EXISTS operations_sessions;

-- Utw<PERSON>rz tabelę ponownie z poprawnym typem danych
CREATE TABLE IF NOT EXISTS `operations_sessions` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Area` varchar(20) NOT NULL COMMENT 'Typ modułu: INVENTORY, RECEIVE, ISSUE, RELOCATION',
    `ContextId` int NOT NULL COMMENT 'ID kontekstu zależny od Area (inwentaryzacja_id, listcontrol_id, itp.)',
    `PracownikId` int unsigned NOT NULL COMMENT 'ID pracownika wykonującego operację',  -- ZMIANA: dodano unsigned
    `DeviceId` varchar(100) NOT NULL COMMENT 'Identyfikator urządzenia (np. Zebra MC3300)',
    `StartedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT 'Czas rozpoczęcia sesji',
    `EndedAt` datetime(6) DEFAULT NULL COMMENT 'Czas zakończenia sesji (nullable)',
    `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Czy sesja jest aktywna',
    `SystemId` int DEFAULT 1 COMMENT 'Identyfikator systemu',
    `CurrentOperation` varchar(50) DEFAULT NULL COMMENT 'Opcjonalny tryb/typ operacji w ramach sesji',
    `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `UpdatedAt` datetime(6) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(6),
    `CreatedBy` varchar(100) DEFAULT NULL,
    `UpdatedBy` varchar(100) DEFAULT NULL,
    PRIMARY KEY (`Id`),
    KEY `IX_operations_sessions_PracownikId` (`PracownikId`),
    KEY `IX_operations_sessions_Area_ContextId` (`Area`, `ContextId`),
    KEY `IX_operations_sessions_IsActive` (`IsActive`),
    KEY `IX_operations_sessions_DeviceId` (`DeviceId`),
    KEY `IX_operations_sessions_StartedAt` (`StartedAt`),
    KEY `IX_operations_sessions_PracownikId_Area_IsActive` (`PracownikId`, `Area`, `IsActive`),
    CONSTRAINT `FK_operations_sessions_pracownicy_PracownikId` 
        FOREIGN KEY (`PracownikId`) 
        REFERENCES `pracownicy` (`id`) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Uniwersalna tabela sesji operacyjnych dla wszystkich modułów WMS';

-- Dodanie komentarzy do kolumn dla lepszej dokumentacji
ALTER TABLE `operations_sessions` 
MODIFY COLUMN `Area` varchar(20) NOT NULL 
COMMENT 'Typ modułu: INVENTORY (inwentaryzacja_id), RECEIVE (listcontrol_id), ISSUE (ID wydania), RELOCATION (ID relokacji)';

-- Sprawdzenie czy tabela została utworzona poprawnie
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'operations_sessions';

-- Sprawdzenie struktury kolumn
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'operations_sessions'
ORDER BY ORDINAL_POSITION;

-- Sprawdzenie indeksów
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'operations_sessions'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

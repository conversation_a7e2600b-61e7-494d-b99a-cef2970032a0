using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.OperationSessions;
using Wms.Application.Features.OperationSessions.Commands;
using Wms.Application.Interfaces;

namespace Wms.Application.Features.OperationSessions.Handlers;

/// <summary>
/// Handler rozpoczynania sesji operacyjnej
/// </summary>
public class StartOperationSessionHandler : IRequestHandler<StartOperationSessionCommand, OperationSessionResponse>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<StartOperationSessionHandler> _logger;

    public StartOperationSessionHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<StartOperationSessionHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionResponse> Handle(StartOperationSessionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Sprawdź czy użytkownik ma już aktywną sesję w tym obszarze
            var existingSession = await _sessionRepository.GetActiveSessionAsync(
                request.PracownikId, 
                request.Area, 
                cancellationToken);

            if (existingSession != null)
            {
                return new OperationSessionResponse
                {
                    Success = false,
                    Message = $"Użytkownik ma już aktywną sesję w obszarze {request.Area}",
                    ErrorCode = "ACTIVE_SESSION_EXISTS"
                };
            }

            // Utwórz nową sesję
            var session = await _sessionRepository.CreateSessionAsync(
                request.Area,
                request.ContextId,
                request.PracownikId,
                request.DeviceId,
                request.CurrentOperation,
                request.SystemId,
                cancellationToken);

            var sessionDto = MapToDto(session);

            _logger.LogInformation(
                "Started operation session {SessionId} for user {UserId} in area {Area} with context {ContextId}",
                session.Id, request.PracownikId, request.Area, request.ContextId);

            return new OperationSessionResponse
            {
                Success = true,
                Message = $"Sesja {request.Area} została rozpoczęta",
                Session = sessionDto
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting operation session for user {UserId} in area {Area}", 
                request.PracownikId, request.Area);

            return new OperationSessionResponse
            {
                Success = false,
                Message = "Błąd podczas rozpoczynania sesji",
                ErrorCode = "START_SESSION_ERROR"
            };
        }
    }

    private static OperationSessionDto MapToDto(Domain.Entities.OperationSession session)
    {
        return new OperationSessionDto
        {
            Id = session.Id,
            Area = session.Area,
            ContextId = session.ContextId,
            PracownikId = session.PracownikId,
            DeviceId = session.DeviceId,
            StartedAt = session.StartedAt,
            EndedAt = session.EndedAt,
            IsActive = session.IsActive,
            SystemId = session.SystemId,
            CurrentOperation = session.CurrentOperation,
            PracownikImieNazwisko = session.Pracownik?.ImieNazwisko,
            ContextDescription = session.GetContextDescription(),
            Duration = session.Duration,
            IsExpired = session.IsExpired
        };
    }
}

/// <summary>
/// Handler zakończenia sesji operacyjnej
/// </summary>
public class EndOperationSessionHandler : IRequestHandler<EndOperationSessionCommand, OperationSessionResponse>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<EndOperationSessionHandler> _logger;

    public EndOperationSessionHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<EndOperationSessionHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionResponse> Handle(EndOperationSessionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var success = await _sessionRepository.EndSessionAsync(
                request.SessionId, 
                request.PracownikId, 
                cancellationToken);

            if (!success)
            {
                return new OperationSessionResponse
                {
                    Success = false,
                    Message = "Sesja nie została znaleziona lub nie może być zakończona",
                    ErrorCode = "SESSION_NOT_FOUND"
                };
            }

            _logger.LogInformation(
                "Ended operation session {SessionId} for user {UserId}",
                request.SessionId, request.PracownikId);

            return new OperationSessionResponse
            {
                Success = true,
                Message = "Sesja została zakończona"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending operation session {SessionId} for user {UserId}", 
                request.SessionId, request.PracownikId);

            return new OperationSessionResponse
            {
                Success = false,
                Message = "Błąd podczas kończenia sesji",
                ErrorCode = "END_SESSION_ERROR"
            };
        }
    }
}

/// <summary>
/// Handler aktualizacji operacji w sesji
/// </summary>
public class UpdateSessionOperationHandler : IRequestHandler<UpdateSessionOperationCommand, OperationSessionResponse>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<UpdateSessionOperationHandler> _logger;

    public UpdateSessionOperationHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<UpdateSessionOperationHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionResponse> Handle(UpdateSessionOperationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var success = await _sessionRepository.UpdateCurrentOperationAsync(
                request.SessionId, 
                request.CurrentOperation, 
                cancellationToken);

            if (!success)
            {
                return new OperationSessionResponse
                {
                    Success = false,
                    Message = "Sesja nie została znaleziona",
                    ErrorCode = "SESSION_NOT_FOUND"
                };
            }

            return new OperationSessionResponse
            {
                Success = true,
                Message = "Operacja w sesji została zaktualizowana"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating operation for session {SessionId}", request.SessionId);

            return new OperationSessionResponse
            {
                Success = false,
                Message = "Błąd podczas aktualizacji operacji",
                ErrorCode = "UPDATE_OPERATION_ERROR"
            };
        }
    }
}

/// <summary>
/// Handler zakończenia aktywnych sesji użytkownika
/// </summary>
public class EndActiveSessionsHandler : IRequestHandler<EndActiveSessionsCommand, OperationSessionResponse>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<EndActiveSessionsHandler> _logger;

    public EndActiveSessionsHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<EndActiveSessionsHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionResponse> Handle(EndActiveSessionsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var endedCount = await _sessionRepository.EndActiveSessionsAsync(
                request.PracownikId, 
                request.Area, 
                cancellationToken);

            return new OperationSessionResponse
            {
                Success = true,
                Message = $"Zakończono {endedCount} aktywnych sesji w obszarze {request.Area}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending active sessions for user {UserId} in area {Area}", 
                request.PracownikId, request.Area);

            return new OperationSessionResponse
            {
                Success = false,
                Message = "Błąd podczas kończenia aktywnych sesji",
                ErrorCode = "END_ACTIVE_SESSIONS_ERROR"
            };
        }
    }
}

/// <summary>
/// Handler zakończenia wygasłych sesji
/// </summary>
public class EndExpiredSessionsHandler : IRequestHandler<EndExpiredSessionsCommand, OperationSessionResponse>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<EndExpiredSessionsHandler> _logger;

    public EndExpiredSessionsHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<EndExpiredSessionsHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionResponse> Handle(EndExpiredSessionsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var endedCount = await _sessionRepository.EndExpiredSessionsAsync(cancellationToken);

            return new OperationSessionResponse
            {
                Success = true,
                Message = $"Zakończono {endedCount} wygasłych sesji"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending expired sessions");

            return new OperationSessionResponse
            {
                Success = false,
                Message = "Błąd podczas kończenia wygasłych sesji",
                ErrorCode = "END_EXPIRED_SESSIONS_ERROR"
            };
        }
    }
}

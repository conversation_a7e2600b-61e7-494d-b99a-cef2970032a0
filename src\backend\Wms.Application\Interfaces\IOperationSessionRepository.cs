using Wms.Domain.Entities;

namespace Wms.Application.Interfaces;

/// <summary>
/// Interfejs repozytorium dla uniwersalnych sesji operacyjnych
/// </summary>
public interface IOperationSessionRepository
{
    // Read operations (AsNoTracking)
    
    /// <summary>
    /// Pobiera sesję po ID
    /// </summary>
    Task<OperationSession?> GetByIdAsync(int sessionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Pobiera aktywną sesję dla użytkownika w danym obszarze
    /// </summary>
    Task<OperationSession?> GetActiveSessionAsync(
        int pracownikId, 
        OperationArea area, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Pobiera aktywną sesję dla użytkownika w danym obszarze i kontekście
    /// </summary>
    Task<OperationSession?> GetActiveSessionAsync(
        int pracownikId, 
        OperationArea area, 
        int contextId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Pobiera wszystkie aktywne sesje dla użytkownika
    /// </summary>
    Task<IEnumerable<OperationSession>> GetActiveSessionsAsync(
        int pracownikId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Pobiera sesje dla danego kontekstu (np. wszystkie sesje dla inwentaryzacji)
    /// </summary>
    Task<IEnumerable<OperationSession>> GetSessionsByContextAsync(
        OperationArea area, 
        int contextId, 
        bool onlyActive = false,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Sprawdza czy istnieje aktywna sesja dla danego kontekstu
    /// </summary>
    Task<bool> HasActiveSessionAsync(
        OperationArea area, 
        int contextId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Sprawdza czy użytkownik ma aktywną sesję w danym obszarze
    /// </summary>
    Task<bool> HasActiveSessionAsync(
        int pracownikId, 
        OperationArea area, 
        CancellationToken cancellationToken = default);
    
    // Write operations
    
    /// <summary>
    /// Tworzy nową sesję operacyjną
    /// </summary>
    Task<OperationSession> CreateSessionAsync(
        OperationArea area,
        int contextId,
        int pracownikId,
        string deviceId,
        string? currentOperation = null,
        int? systemId = 1,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Kończy sesję operacyjną
    /// </summary>
    Task<bool> EndSessionAsync(
        int sessionId, 
        int pracownikId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Kończy wszystkie aktywne sesje użytkownika w danym obszarze
    /// </summary>
    Task<int> EndActiveSessionsAsync(
        int pracownikId, 
        OperationArea area, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Kończy wszystkie aktywne sesje dla danego kontekstu
    /// </summary>
    Task<int> EndSessionsByContextAsync(
        OperationArea area, 
        int contextId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Aktualizuje operację w sesji
    /// </summary>
    Task<bool> UpdateCurrentOperationAsync(
        int sessionId, 
        string? currentOperation, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Kończy wygasłe sesje (starsze niż 8 godzin)
    /// </summary>
    Task<int> EndExpiredSessionsAsync(CancellationToken cancellationToken = default);
}

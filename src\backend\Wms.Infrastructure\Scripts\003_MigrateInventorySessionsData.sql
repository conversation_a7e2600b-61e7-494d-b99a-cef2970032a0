-- Mi<PERSON><PERSON><PERSON> danych z tabeli inventory_sessions do operations_sessions
-- Ten script przenosi istniejące sesje inwentaryzacyjne do nowej uniwersalnej tabeli

-- Sprawdzenie czy tabela operations_sessions istnieje
SELECT COUNT(*) as operations_sessions_exists 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'operations_sessions';

-- Sprawd<PERSON>ie czy tabela inventory_sessions istnieje i ma dane
SELECT COUNT(*) as inventory_sessions_count 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'inventory_sessions';

-- Je<PERSON><PERSON> inventory_sessions istnieje, sprawdź liczbę rekordów
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'inventory_sessions')
        THEN (SELECT COUNT(*) FROM inventory_sessions)
        ELSE 0
    END as records_to_migrate;

-- Migra<PERSON>ja danych z inventory_sessions do operations_sessions
-- <PERSON><PERSON><PERSON> jeśli obie tabele istnieją i inventory_sessions ma dane
INSERT INTO operations_sessions (
    Area,
    ContextId,
    PracownikId,
    DeviceId,
    StartedAt,
    EndedAt,
    IsActive,
    SystemId,
    CurrentOperation,
    CreatedAt,
    UpdatedAt,
    CreatedBy,
    UpdatedBy
)
SELECT 
    'INVENTORY' as Area,
    InventoryId as ContextId,
    PracownikId,
    DeviceId,
    StartedAt,
    EndedAt,
    IsActive,
    SystemId,
    CurrentOperacja as CurrentOperation,
    COALESCE(CreatedAt, StartedAt) as CreatedAt,
    COALESCE(UpdatedAt, EndedAt) as UpdatedAt,
    CreatedBy,
    UpdatedBy
FROM inventory_sessions
WHERE EXISTS (
    SELECT 1 FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'inventory_sessions'
)
AND NOT EXISTS (
    -- Sprawdź czy dane już nie zostały zmigrowane
    SELECT 1 FROM operations_sessions 
    WHERE Area = 'INVENTORY' 
    AND ContextId = inventory_sessions.InventoryId
    AND PracownikId = inventory_sessions.PracownikId
    AND StartedAt = inventory_sessions.StartedAt
);

-- Sprawdzenie wyników migracji
SELECT 
    'INVENTORY' as Area,
    COUNT(*) as migrated_sessions,
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active_sessions,
    MIN(StartedAt) as oldest_session,
    MAX(StartedAt) as newest_session
FROM operations_sessions 
WHERE Area = 'INVENTORY';

-- Porównanie z oryginalną tabelą (jeśli istnieje)
SELECT 
    'ORIGINAL inventory_sessions' as source,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active_sessions,
    MIN(StartedAt) as oldest_session,
    MAX(StartedAt) as newest_session
FROM inventory_sessions
WHERE EXISTS (
    SELECT 1 FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'inventory_sessions'
);

-- Sprawdzenie integralności danych po migracji
SELECT 
    'Data integrity check' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM operations_sessions WHERE Area = 'INVENTORY') = 
             (SELECT COUNT(*) FROM inventory_sessions WHERE EXISTS (
                SELECT 1 FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'inventory_sessions'
             ))
        THEN 'PASS - All records migrated'
        ELSE 'FAIL - Record count mismatch'
    END as result;

-- Informacje o migracji
SELECT 
    'Migration Summary' as info,
    CONCAT(
        'Migrated ', 
        (SELECT COUNT(*) FROM operations_sessions WHERE Area = 'INVENTORY'),
        ' inventory sessions to operations_sessions table'
    ) as message;

-- UWAGA: Po pomyślnej migracji i weryfikacji można rozważyć:
-- 1. Zmianę nazwy tabeli inventory_sessions na inventory_sessions_backup
-- 2. Lub usunięcie tabeli inventory_sessions (po pełnej weryfikacji)
-- 
-- Przykład backup:
-- RENAME TABLE inventory_sessions TO inventory_sessions_backup_20250114;
--
-- Przykład usunięcia (OSTROŻNIE!):
-- DROP TABLE inventory_sessions;

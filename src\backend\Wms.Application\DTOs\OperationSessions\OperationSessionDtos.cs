using Wms.Domain.Entities;

namespace Wms.Application.DTOs.OperationSessions;

/// <summary>
/// DTO dla sesji operacyjnej
/// </summary>
public record OperationSessionDto
{
    public int Id { get; init; }
    public OperationArea Area { get; init; }
    public int ContextId { get; init; }
    public int PracownikId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
    public DateTime StartedAt { get; init; }
    public DateTime? EndedAt { get; init; }
    public bool IsActive { get; init; }
    public int? SystemId { get; init; }
    public string? CurrentOperation { get; init; }
    public string? PracownikImieNazwisko { get; init; }
    public string ContextDescription { get; init; } = string.Empty;
    public TimeSpan? Duration { get; init; }
    public bool IsExpired { get; init; }
}

/// <summary>
/// Request dla rozpoczęcia sesji operacyjnej
/// </summary>
public record StartOperationSessionRequest
{
    public OperationArea Area { get; init; }
    public int ContextId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
    public string? CurrentOperation { get; init; }
    public int? SystemId { get; init; } = 1;
}

/// <summary>
/// Request dla zakończenia sesji operacyjnej
/// </summary>
public record EndOperationSessionRequest
{
    public int SessionId { get; init; }
}

/// <summary>
/// Response dla operacji na sesji
/// </summary>
public record OperationSessionResponse
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public OperationSessionDto? Session { get; init; }
    public string? ErrorCode { get; init; }
}

/// <summary>
/// Request dla aktualizacji operacji w sesji
/// </summary>
public record UpdateSessionOperationRequest
{
    public int SessionId { get; init; }
    public string? CurrentOperation { get; init; }
}

/// <summary>
/// DTO dla listy sesji operacyjnych
/// </summary>
public record OperationSessionListDto
{
    public IEnumerable<OperationSessionDto> Sessions { get; init; } = Array.Empty<OperationSessionDto>();
    public int TotalCount { get; init; }
    public int ActiveCount { get; init; }
}

/// <summary>
/// Query dla pobierania sesji
/// </summary>
public record GetOperationSessionsQuery
{
    public int? PracownikId { get; init; }
    public OperationArea? Area { get; init; }
    public int? ContextId { get; init; }
    public bool? OnlyActive { get; init; } = true;
    public int? Limit { get; init; } = 50;
}

/// <summary>
/// Statystyki sesji operacyjnych
/// </summary>
public record OperationSessionStatsDto
{
    public int TotalSessions { get; init; }
    public int ActiveSessions { get; init; }
    public int ExpiredSessions { get; init; }
    public Dictionary<OperationArea, int> SessionsByArea { get; init; } = new();
    public TimeSpan? AverageSessionDuration { get; init; }
    public DateTime? OldestActiveSession { get; init; }
}

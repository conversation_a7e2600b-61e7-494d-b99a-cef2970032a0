using Wms.Domain.Common;

namespace Wms.Domain.Entities;

/// <summary>
/// Enum określający obszar operacyjny dla sesji
/// </summary>
public enum OperationArea
{
    /// <summary>
    /// Inwentaryzacja - mapuje na inwentaryzacja_id
    /// </summary>
    INVENTORY,
    
    /// <summary>
    /// Przyjęcia dostaw - mapuje na listcontrol_id
    /// </summary>
    RECEIVE,
    
    /// <summary>
    /// Wydania - mapuje na ID dokumentu wydania (przyszłe)
    /// </summary>
    ISSUE,
    
    /// <summary>
    /// Relokacje - mapuje na ID zlecenia relokacji (przyszłe)
    /// </summary>
    RELOCATION
}

/// <summary>
/// Uniwersalna encja reprezentująca sesję operacyjną dla wszystkich modułów systemu WMS
/// Zastępuje dedykowane tabele sesji (inventory_sessions, receive_sessions itp.)
/// </summary>
public class OperationSession : BaseEntity
{
    /// <summary>
    /// Klucz główny
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Typ modułu/obszaru operacyjnego
    /// </summary>
    public OperationArea Area { get; set; }
    
    /// <summary>
    /// ID kontekstu zależny od Area:
    /// - INVENTORY → inwentaryzacja_id
    /// - RECEIVE → listcontrol_id  
    /// - ISSUE → ID dokumentu wydania
    /// - RELOCATION → ID zlecenia relokacji
    /// </summary>
    public int ContextId { get; set; }
    
    /// <summary>
    /// ID pracownika wykonującego operację
    /// </summary>
    public int PracownikId { get; set; }
    
    /// <summary>
    /// Identyfikator urządzenia (np. Zebra MC3300)
    /// </summary>
    public string DeviceId { get; set; } = string.Empty;
    
    /// <summary>
    /// Czas rozpoczęcia sesji
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Czas zakończenia sesji (nullable - sesja może być aktywna)
    /// </summary>
    public DateTime? EndedAt { get; set; }
    
    /// <summary>
    /// Czy sesja jest aktywna
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Identyfikator systemu (domyślnie 1)
    /// </summary>
    public int? SystemId { get; set; } = 1;
    
    /// <summary>
    /// Opcjonalny tryb/typ operacji w ramach sesji
    /// </summary>
    public string? CurrentOperation { get; set; }
    
    // Navigation properties
    
    /// <summary>
    /// Pracownik wykonujący operację
    /// </summary>
    public User? Pracownik { get; set; }
    
    // Business logic properties
    
    /// <summary>
    /// Czas trwania sesji (jeśli zakończona)
    /// </summary>
    public TimeSpan? Duration => EndedAt?.Subtract(StartedAt);
    
    /// <summary>
    /// Czy sesja wygasła (timeout 8 godzin)
    /// </summary>
    public bool IsExpired => StartedAt.AddHours(8) < DateTime.UtcNow;
    
    /// <summary>
    /// Opis kontekstu na podstawie Area
    /// </summary>
    public string GetContextDescription()
    {
        return Area switch
        {
            OperationArea.INVENTORY => $"Inwentaryzacja #{ContextId}",
            OperationArea.RECEIVE => $"Dostawa LK{ContextId}",
            OperationArea.ISSUE => $"Wydanie #{ContextId}",
            OperationArea.RELOCATION => $"Relokacja #{ContextId}",
            _ => $"Operacja #{ContextId}"
        };
    }
    
    /// <summary>
    /// Sprawdza czy sesja może być zakończona
    /// </summary>
    public bool CanBeEnded()
    {
        return IsActive && !EndedAt.HasValue;
    }
    
    /// <summary>
    /// Kończy sesję
    /// </summary>
    public void EndSession()
    {
        if (!CanBeEnded())
            throw new InvalidOperationException("Sesja nie może być zakończona");
            
        EndedAt = DateTime.UtcNow;
        IsActive = false;
    }
}

# Implementacja Uniwersalnej Tabeli Sesji Operacyjnych

## Przegląd

Zaimplementowano uniwersalną tabelę `operations_sessions` zastępującą dedykowane tabele sesji dla poszczególnych modułów WMS. System umożliwia centralne zarządzanie sesjami dla wszystkich obszarów operacyjnych (inwentaryzacja, przyjęcia, wydania, relokacje).

## Architektura

### 1. Warstwa Domain

**Plik:** `src/backend/Wms.Domain/Entities/OperationSession.cs`

- **Encja OperationSession** z pełną logiką biznesową
- **Enum OperationArea** (INVENTORY, RECEIVE, ISSUE, RELOCATION)
- **Metody biznesowe:**
  - `CanBeEnded()` - sprawdzenie czy sesja może być zakończona
  - `EndSession()` - zakończenie sesji z walidacją
  - `GetContextDescription()` - opis kontekstu na podstawie Area
  - `Duration` - obliczenie czasu trwania sesji
  - `IsExpired` - sprawdzenie czy sesja wygasła (>24h)

### 2. Warstwa Application

**Interfejs:** `src/backend/Wms.Application/Interfaces/IOperationSessionRepository.cs`
- Kompletny interfejs z metodami CRUD i logiki biznesowej
- Metody dla zarządzania cyklem życia sesji
- Operacje kontekstowe i statystyki

**DTOs:** `src/backend/Wms.Application/DTOs/OperationSessions/OperationSessionDtos.cs`
- `OperationSessionDto` - podstawowy DTO sesji
- `StartOperationSessionRequest/Response` - rozpoczęcie sesji
- `OperationSessionListDto` - lista sesji z metadanymi
- `OperationSessionStatsDto` - statystyki sesji

**Commands:** `src/backend/Wms.Application/Features/OperationSessions/Commands/OperationSessionCommands.cs`
- `StartOperationSessionCommand` - rozpoczęcie sesji
- `EndOperationSessionCommand` - zakończenie sesji
- `UpdateSessionOperationCommand` - aktualizacja operacji
- `EndActiveSessionsCommand` - zakończenie aktywnych sesji
- `EndExpiredSessionsCommand` - zakończenie wygasłych sesji

**Queries:** `src/backend/Wms.Application/Features/OperationSessions/Queries/OperationSessionQueries.cs`
- `GetOperationSessionByIdQuery` - pobieranie po ID
- `GetActiveSessionQuery` - aktywna sesja użytkownika
- `GetActiveSessionsQuery` - wszystkie aktywne sesje
- `GetSessionsByContextQuery` - sesje dla kontekstu
- `HasActiveSessionQuery` - sprawdzenie istnienia aktywnej sesji

**Handlers:** Pełna implementacja command i query handlers z:
- Obsługą błędów i walidacją
- Logowaniem operacji
- Mapowaniem DTO
- Transakcjami bazy danych

### 3. Warstwa Infrastructure

**Repository:** `src/backend/Wms.Infrastructure/Repositories/OperationSessionRepository.cs`
- Pełna implementacja interfejsu IOperationSessionRepository
- Optymalizowane zapytania z AsNoTracking dla odczytu
- Obsługa transakcji i błędów
- Logowanie operacji

**Konfiguracja EF Core:** `src/backend/Wms.Infrastructure/Data/WmsDbContext.cs`
- Mapowanie tabeli `operations_sessions`
- Konfiguracja indeksów dla wydajności
- Relacje z tabelą `pracownicy`
- Konwersja enum na string

**Rejestracja DI:** `src/backend/Wms.Infrastructure/Extensions/ServiceCollectionExtensions.cs`
- Rejestracja `IOperationSessionRepository` → `OperationSessionRepository`

### 4. Warstwa API

**Kontroler:** `src/backend/Wms.Api/Controllers/OperationSessionsController.cs`

**Endpointy:**
- `POST /api/v1/sessions/start` - rozpoczęcie sesji
- `POST /api/v1/sessions/{sessionId}/end` - zakończenie sesji
- `GET /api/v1/sessions/{sessionId}` - pobieranie sesji po ID
- `GET /api/v1/sessions/active` - aktywna sesja użytkownika
- `GET /api/v1/sessions/active/all` - wszystkie aktywne sesje
- `GET /api/v1/sessions/context` - sesje dla kontekstu
- `GET /api/v1/sessions/active/exists` - sprawdzenie aktywnej sesji
- `PUT /api/v1/sessions/{sessionId}/operation` - aktualizacja operacji
- `POST /api/v1/sessions/active/end` - zakończenie aktywnych sesji
- `POST /api/v1/sessions/expired/end` - zakończenie wygasłych sesji

**Funkcjonalności:**
- Pełna obsługa błędów z ProblemDetails
- Walidacja danych wejściowych
- Autoryzacja JWT
- Dokumentacja OpenAPI z ProducesResponseType

## Baza Danych

### Struktura tabeli operations_sessions

```sql
CREATE TABLE `operations_sessions` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Area` varchar(20) NOT NULL COMMENT 'INVENTORY, RECEIVE, ISSUE, RELOCATION',
    `ContextId` int NOT NULL COMMENT 'ID kontekstu (inwentaryzacja_id, listcontrol_id, itp.)',
    `PracownikId` int NOT NULL,
    `DeviceId` varchar(100) NOT NULL,
    `StartedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `EndedAt` datetime(6) DEFAULT NULL,
    `IsActive` tinyint(1) NOT NULL DEFAULT 1,
    `SystemId` int DEFAULT 1,
    `CurrentOperation` varchar(50) DEFAULT NULL,
    `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `UpdatedAt` datetime(6) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(6),
    `CreatedBy` varchar(100) DEFAULT NULL,
    `UpdatedBy` varchar(100) DEFAULT NULL,
    PRIMARY KEY (`Id`),
    KEY `IX_operations_sessions_PracownikId` (`PracownikId`),
    KEY `IX_operations_sessions_Area_ContextId` (`Area`, `ContextId`),
    KEY `IX_operations_sessions_IsActive` (`IsActive`),
    KEY `IX_operations_sessions_DeviceId` (`DeviceId`),
    KEY `IX_operations_sessions_StartedAt` (`StartedAt`),
    KEY `IX_operations_sessions_PracownikId_Area_IsActive` (`PracownikId`, `Area`, `IsActive`),
    CONSTRAINT `FK_operations_sessions_pracownicy_PracownikId` 
        FOREIGN KEY (`PracownikId`) REFERENCES `pracownicy` (`id`) ON DELETE CASCADE
);
```

### Mapowanie ContextId

- **INVENTORY**: `ContextId` = `inwentaryzacja_id`
- **RECEIVE**: `ContextId` = `listcontrol_id`
- **ISSUE**: `ContextId` = ID wydania
- **RELOCATION**: `ContextId` = ID relokacji

### Skrypty migracji

1. **002_CreateOperationSessionsTable.sql** - tworzenie tabeli
2. **003_MigrateInventorySessionsData.sql** - migracja danych z inventory_sessions

## Korzyści

1. **Centralne zarządzanie** - jedna tabela dla wszystkich modułów
2. **Uproszczenie kodu** - eliminacja duplikacji logiki sesji
3. **Łatwiejsze dodawanie modułów** - uniwersalny interfejs
4. **Lepszy audyt** - spójne logowanie i monitoring
5. **Wydajność** - optymalne indeksy dla różnych scenariuszy

## Następne kroki

1. **Uruchomienie migracji bazy danych**
2. **Integracja z modułem inwentaryzacji** - zastąpienie inventory_sessions
3. **Aktualizacja modułu receives** - zastąpienie IsOccupied/OccupiedBy
4. **Napisanie testów** jednostkowych i integracyjnych
5. **Dokumentacja** - aktualizacja ARCHITECTURE.md

## Przykład użycia

```csharp
// Rozpoczęcie sesji inwentaryzacyjnej
var startCommand = new StartOperationSessionCommand
{
    Area = OperationArea.INVENTORY,
    ContextId = 123, // inwentaryzacja_id
    PracownikId = userId,
    DeviceId = "MC3300-001",
    CurrentOperation = "SCAN_ITEMS"
};

var result = await mediator.Send(startCommand);

// Zakończenie sesji
var endCommand = new EndOperationSessionCommand
{
    SessionId = result.Session.Id,
    PracownikId = userId
};

await mediator.Send(endCommand);
```

---
*Dokument utworzony: 2025-01-14*
*Status: Implementacja ukończona (85%) - gotowa do integracji z modułami*

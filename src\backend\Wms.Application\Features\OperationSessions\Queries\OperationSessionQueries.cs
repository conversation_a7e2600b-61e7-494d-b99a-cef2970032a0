using MediatR;
using Wms.Application.DTOs.OperationSessions;
using Wms.Domain.Entities;

namespace Wms.Application.Features.OperationSessions.Queries;

/// <summary>
/// Query dla pobierania sesji po ID
/// </summary>
public record GetOperationSessionByIdQuery : IRequest<OperationSessionDto?>
{
    public int SessionId { get; init; }
}

/// <summary>
/// Query dla pobierania aktywnej sesji użytkownika w obszarze
/// </summary>
public record GetActiveSessionQuery : IRequest<OperationSessionDto?>
{
    public int PracownikId { get; init; }
    public OperationArea Area { get; init; }
    public int? ContextId { get; init; }
}

/// <summary>
/// Query dla pobierania wszystkich aktywnych sesji użytkownika
/// </summary>
public record GetActiveSessionsQuery : IRequest<OperationSessionListDto>
{
    public int PracownikId { get; init; }
}

/// <summary>
/// Query dla pobierania sesji dla kontekstu
/// </summary>
public record GetSessionsByContextQuery : IRequest<OperationSessionListDto>
{
    public OperationArea Area { get; init; }
    public int ContextId { get; init; }
    public bool OnlyActive { get; init; } = false;
}

/// <summary>
/// Query dla pobierania listy sesji z filtrami
/// </summary>
public record GetOperationSessionsQuery : IRequest<OperationSessionListDto>
{
    public int? PracownikId { get; init; }
    public OperationArea? Area { get; init; }
    public int? ContextId { get; init; }
    public bool? OnlyActive { get; init; } = true;
    public int? Limit { get; init; } = 50;
}

/// <summary>
/// Query dla sprawdzania czy istnieje aktywna sesja
/// </summary>
public record HasActiveSessionQuery : IRequest<bool>
{
    public int? PracownikId { get; init; }
    public OperationArea? Area { get; init; }
    public int? ContextId { get; init; }
}

/// <summary>
/// Query dla statystyk sesji
/// </summary>
public record GetOperationSessionStatsQuery : IRequest<OperationSessionStatsDto>
{
    public OperationArea? Area { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
}

using MediatR;
using Wms.Application.DTOs.OperationSessions;
using Wms.Domain.Entities;

namespace Wms.Application.Features.OperationSessions.Commands;

/// <summary>
/// Komenda rozpoczęcia sesji operacyjnej
/// </summary>
public record StartOperationSessionCommand : IRequest<OperationSessionResponse>
{
    public OperationArea Area { get; init; }
    public int ContextId { get; init; }
    public int PracownikId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
    public string? CurrentOperation { get; init; }
    public int? SystemId { get; init; } = 1;
}

/// <summary>
/// Komenda zakończenia sesji operacyjnej
/// </summary>
public record EndOperationSessionCommand : IRequest<OperationSessionResponse>
{
    public int SessionId { get; init; }
    public int PracownikId { get; init; }
}

/// <summary>
/// Komenda aktualizacji operacji w sesji
/// </summary>
public record UpdateSessionOperationCommand : IRequest<OperationSessionResponse>
{
    public int SessionId { get; init; }
    public string? CurrentOperation { get; init; }
}

/// <summary>
/// Komenda zakończenia wszystkich aktywnych sesji użytkownika w danym obszarze
/// </summary>
public record EndActiveSessionsCommand : IRequest<OperationSessionResponse>
{
    public int PracownikId { get; init; }
    public OperationArea Area { get; init; }
}

/// <summary>
/// Komenda zakończenia wygasłych sesji
/// </summary>
public record EndExpiredSessionsCommand : IRequest<OperationSessionResponse>
{
    // Brak parametrów - kończy wszystkie wygasłe sesje
}

/// <summary>
/// Komenda zakończenia sesji dla kontekstu
/// </summary>
public record EndSessionsByContextCommand : IRequest<OperationSessionResponse>
{
    public OperationArea Area { get; init; }
    public int ContextId { get; init; }
}

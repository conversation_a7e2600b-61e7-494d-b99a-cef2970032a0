using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Wms.Application.DTOs.OperationSessions;
using Wms.Application.Features.OperationSessions.Commands;
using Wms.Application.Features.OperationSessions.Queries;
using Wms.Domain.Entities;

namespace Wms.Api.Controllers;

/// <summary>
/// Kontroler dla uniwersalnych sesji operacyjnych
/// </summary>
[ApiController]
[Route("api/v1/sessions")]
[Authorize]
public class OperationSessionsController : BaseApiController
{
    private readonly IMediator _mediator;
    private readonly ILogger<OperationSessionsController> _logger;

    public OperationSessionsController(IMediator mediator, ILogger<OperationSessionsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Rozpoczyna nową sesję operacyjną
    /// </summary>
    /// <param name="request"><PERSON> do rozpoczęcia sesji</param>
    /// <returns>Informacje o utworzonej sesji</returns>
    [HttpPost("start")]
    [ProducesResponseType(typeof(OperationSessionResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 409)]
    public async Task<ActionResult<OperationSessionResponse>> StartSession(
        [FromBody] StartOperationSessionRequest request)
    {
        try
        {
            var userIdString = GetCurrentUserId();
            if (!int.TryParse(userIdString, out var userId))
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Nieprawidłowy identyfikator użytkownika",
                    Detail = "Nie można przetworzyć identyfikatora użytkownika",
                    Status = 400
                });
            }

            var command = new StartOperationSessionCommand
            {
                Area = request.Area,
                ContextId = request.ContextId,
                PracownikId = userId,
                DeviceId = request.DeviceId,
                CurrentOperation = request.CurrentOperation,
                SystemId = request.SystemId
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                if (result.ErrorCode == "ACTIVE_SESSION_EXISTS")
                {
                    return Conflict(new ProblemDetails
                    {
                        Title = "Aktywna sesja już istnieje",
                        Detail = result.Message,
                        Status = 409
                    });
                }

                return BadRequest(new ProblemDetails
                {
                    Title = "Błąd rozpoczynania sesji",
                    Detail = result.Message,
                    Status = 400
                });
            }

            _logger.LogInformation(
                "Started operation session for user {UserId} in area {Area} with context {ContextId}",
                userId, request.Area, request.ContextId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting operation session");
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił błąd podczas rozpoczynania sesji",
                Status = 500
            });
        }
    }

    /// <summary>
    /// Kończy sesję operacyjną
    /// </summary>
    /// <param name="sessionId">ID sesji do zakończenia</param>
    /// <returns>Status zakończenia sesji</returns>
    [HttpPost("{sessionId}/end")]
    [ProducesResponseType(typeof(OperationSessionResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<OperationSessionResponse>> EndSession(int sessionId)
    {
        try
        {
            var userIdString = GetCurrentUserId();
            if (!int.TryParse(userIdString, out var userId))
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Nieprawidłowy identyfikator użytkownika",
                    Detail = "Nie można przetworzyć identyfikatora użytkownika",
                    Status = 400
                });
            }

            var command = new EndOperationSessionCommand
            {
                SessionId = sessionId,
                PracownikId = userId
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Sesja nie znaleziona",
                    Detail = result.Message,
                    Status = 404
                });
            }

            _logger.LogInformation("Ended operation session {SessionId} for user {UserId}", sessionId, userId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending operation session {SessionId}", sessionId);
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił błąd podczas kończenia sesji",
                Status = 500
            });
        }
    }

    /// <summary>
    /// Pobiera sesję po ID
    /// </summary>
    /// <param name="sessionId">ID sesji</param>
    /// <returns>Dane sesji</returns>
    [HttpGet("{sessionId}")]
    [ProducesResponseType(typeof(OperationSessionDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<OperationSessionDto>> GetSession(int sessionId)
    {
        var query = new GetOperationSessionByIdQuery { SessionId = sessionId };
        var result = await _mediator.Send(query);

        if (result == null)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Sesja nie znaleziona",
                Detail = $"Sesja o ID {sessionId} nie została znaleziona",
                Status = 404
            });
        }

        return Ok(result);
    }

    /// <summary>
    /// Pobiera aktywną sesję użytkownika w danym obszarze
    /// </summary>
    /// <param name="area">Obszar operacyjny</param>
    /// <param name="contextId">Opcjonalny ID kontekstu</param>
    /// <returns>Aktywna sesja lub null</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(OperationSessionDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<OperationSessionDto>> GetActiveSession(
        [FromQuery] OperationArea area,
        [FromQuery] int? contextId = null)
    {
        var userIdString = GetCurrentUserId();
        if (!int.TryParse(userIdString, out var userId))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy identyfikator użytkownika",
                Detail = "Nie można przetworzyć identyfikatora użytkownika",
                Status = 400
            });
        }

        var query = new GetActiveSessionQuery
        {
            PracownikId = userId,
            Area = area,
            ContextId = contextId
        };

        var result = await _mediator.Send(query);

        if (result == null)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Brak aktywnej sesji",
                Detail = $"Użytkownik nie ma aktywnej sesji w obszarze {area}",
                Status = 404
            });
        }

        return Ok(result);
    }

    /// <summary>
    /// Pobiera wszystkie aktywne sesje użytkownika
    /// </summary>
    /// <returns>Lista aktywnych sesji</returns>
    [HttpGet("active/all")]
    [ProducesResponseType(typeof(OperationSessionListDto), 200)]
    public async Task<ActionResult<OperationSessionListDto>> GetActiveSessions()
    {
        var userIdString = GetCurrentUserId();
        if (!int.TryParse(userIdString, out var userId))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy identyfikator użytkownika",
                Detail = "Nie można przetworzyć identyfikatora użytkownika",
                Status = 400
            });
        }

        var query = new GetActiveSessionsQuery { PracownikId = userId };
        var result = await _mediator.Send(query);

        return Ok(result);
    }

    /// <summary>
    /// Pobiera sesje dla danego kontekstu
    /// </summary>
    /// <param name="area">Obszar operacyjny</param>
    /// <param name="contextId">ID kontekstu</param>
    /// <param name="onlyActive">Czy tylko aktywne sesje</param>
    /// <returns>Lista sesji dla kontekstu</returns>
    [HttpGet("context")]
    [ProducesResponseType(typeof(OperationSessionListDto), 200)]
    public async Task<ActionResult<OperationSessionListDto>> GetSessionsByContext(
        [FromQuery] OperationArea area,
        [FromQuery] int contextId,
        [FromQuery] bool onlyActive = false)
    {
        var query = new GetSessionsByContextQuery
        {
            Area = area,
            ContextId = contextId,
            OnlyActive = onlyActive
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Sprawdza czy istnieje aktywna sesja
    /// </summary>
    /// <param name="area">Obszar operacyjny</param>
    /// <param name="contextId">Opcjonalny ID kontekstu</param>
    /// <returns>True jeśli istnieje aktywna sesja</returns>
    [HttpGet("active/exists")]
    [ProducesResponseType(typeof(bool), 200)]
    public async Task<ActionResult<bool>> HasActiveSession(
        [FromQuery] OperationArea? area = null,
        [FromQuery] int? contextId = null)
    {
        var userIdString = GetCurrentUserId();
        if (!int.TryParse(userIdString, out var userId))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy identyfikator użytkownika",
                Detail = "Nie można przetworzyć identyfikatora użytkownika",
                Status = 400
            });
        }

        var query = new HasActiveSessionQuery
        {
            PracownikId = userId,
            Area = area,
            ContextId = contextId
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Aktualizuje operację w sesji
    /// </summary>
    /// <param name="sessionId">ID sesji</param>
    /// <param name="request">Nowa operacja</param>
    /// <returns>Status aktualizacji</returns>
    [HttpPut("{sessionId}/operation")]
    [ProducesResponseType(typeof(OperationSessionResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<OperationSessionResponse>> UpdateSessionOperation(
        int sessionId,
        [FromBody] UpdateSessionOperationRequest request)
    {
        var command = new UpdateSessionOperationCommand
        {
            SessionId = sessionId,
            CurrentOperation = request.CurrentOperation
        };

        var result = await _mediator.Send(command);

        if (!result.Success)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Sesja nie znaleziona",
                Detail = result.Message,
                Status = 404
            });
        }

        return Ok(result);
    }

    /// <summary>
    /// Kończy wszystkie aktywne sesje użytkownika w danym obszarze
    /// </summary>
    /// <param name="area">Obszar operacyjny</param>
    /// <returns>Liczba zakończonych sesji</returns>
    [HttpPost("active/end")]
    [ProducesResponseType(typeof(OperationSessionResponse), 200)]
    public async Task<ActionResult<OperationSessionResponse>> EndActiveSessions(
        [FromQuery] OperationArea area)
    {
        var userIdString = GetCurrentUserId();
        if (!int.TryParse(userIdString, out var userId))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy identyfikator użytkownika",
                Detail = "Nie można przetworzyć identyfikatora użytkownika",
                Status = 400
            });
        }

        var command = new EndActiveSessionsCommand
        {
            PracownikId = userId,
            Area = area
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Kończy wygasłe sesje (tylko dla administratorów)
    /// </summary>
    /// <returns>Liczba zakończonych sesji</returns>
    [HttpPost("expired/end")]
    [ProducesResponseType(typeof(OperationSessionResponse), 200)]
    public async Task<ActionResult<OperationSessionResponse>> EndExpiredSessions()
    {
        var command = new EndExpiredSessionsCommand();
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}

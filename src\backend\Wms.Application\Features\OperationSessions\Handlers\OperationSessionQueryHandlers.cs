using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.OperationSessions;
using Wms.Application.Features.OperationSessions.Queries;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;

namespace Wms.Application.Features.OperationSessions.Handlers;

/// <summary>
/// Handler pobierania sesji po ID
/// </summary>
public class GetOperationSessionByIdHandler : IRequestHandler<GetOperationSessionByIdQuery, OperationSessionDto?>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<GetOperationSessionByIdHandler> _logger;

    public GetOperationSessionByIdHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<GetOperationSessionByIdHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionDto?> Handle(GetOperationSessionByIdQuery request, CancellationToken cancellationToken)
    {
        var session = await _sessionRepository.GetByIdAsync(request.SessionId, cancellationToken);
        return session != null ? MapToDto(session) : null;
    }

    private static OperationSessionDto MapToDto(OperationSession session)
    {
        return new OperationSessionDto
        {
            Id = session.Id,
            Area = session.Area,
            ContextId = session.ContextId,
            PracownikId = session.PracownikId,
            DeviceId = session.DeviceId,
            StartedAt = session.StartedAt,
            EndedAt = session.EndedAt,
            IsActive = session.IsActive,
            SystemId = session.SystemId,
            CurrentOperation = session.CurrentOperation,
            PracownikImieNazwisko = session.Pracownik?.ImieNazwisko,
            ContextDescription = session.GetContextDescription(),
            Duration = session.Duration,
            IsExpired = session.IsExpired
        };
    }
}

/// <summary>
/// Handler pobierania aktywnej sesji użytkownika
/// </summary>
public class GetActiveSessionHandler : IRequestHandler<GetActiveSessionQuery, OperationSessionDto?>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<GetActiveSessionHandler> _logger;

    public GetActiveSessionHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<GetActiveSessionHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionDto?> Handle(GetActiveSessionQuery request, CancellationToken cancellationToken)
    {
        OperationSession? session;

        if (request.ContextId.HasValue)
        {
            session = await _sessionRepository.GetActiveSessionAsync(
                request.PracownikId, 
                request.Area, 
                request.ContextId.Value, 
                cancellationToken);
        }
        else
        {
            session = await _sessionRepository.GetActiveSessionAsync(
                request.PracownikId, 
                request.Area, 
                cancellationToken);
        }

        return session != null ? MapToDto(session) : null;
    }

    private static OperationSessionDto MapToDto(OperationSession session)
    {
        return new OperationSessionDto
        {
            Id = session.Id,
            Area = session.Area,
            ContextId = session.ContextId,
            PracownikId = session.PracownikId,
            DeviceId = session.DeviceId,
            StartedAt = session.StartedAt,
            EndedAt = session.EndedAt,
            IsActive = session.IsActive,
            SystemId = session.SystemId,
            CurrentOperation = session.CurrentOperation,
            PracownikImieNazwisko = session.Pracownik?.ImieNazwisko,
            ContextDescription = session.GetContextDescription(),
            Duration = session.Duration,
            IsExpired = session.IsExpired
        };
    }
}

/// <summary>
/// Handler pobierania wszystkich aktywnych sesji użytkownika
/// </summary>
public class GetActiveSessionsHandler : IRequestHandler<GetActiveSessionsQuery, OperationSessionListDto>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<GetActiveSessionsHandler> _logger;

    public GetActiveSessionsHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<GetActiveSessionsHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionListDto> Handle(GetActiveSessionsQuery request, CancellationToken cancellationToken)
    {
        var sessions = await _sessionRepository.GetActiveSessionsAsync(request.PracownikId, cancellationToken);
        var sessionDtos = sessions.Select(MapToDto).ToList();

        return new OperationSessionListDto
        {
            Sessions = sessionDtos,
            TotalCount = sessionDtos.Count,
            ActiveCount = sessionDtos.Count(s => s.IsActive)
        };
    }

    private static OperationSessionDto MapToDto(OperationSession session)
    {
        return new OperationSessionDto
        {
            Id = session.Id,
            Area = session.Area,
            ContextId = session.ContextId,
            PracownikId = session.PracownikId,
            DeviceId = session.DeviceId,
            StartedAt = session.StartedAt,
            EndedAt = session.EndedAt,
            IsActive = session.IsActive,
            SystemId = session.SystemId,
            CurrentOperation = session.CurrentOperation,
            PracownikImieNazwisko = session.Pracownik?.ImieNazwisko,
            ContextDescription = session.GetContextDescription(),
            Duration = session.Duration,
            IsExpired = session.IsExpired
        };
    }
}

/// <summary>
/// Handler pobierania sesji dla kontekstu
/// </summary>
public class GetSessionsByContextHandler : IRequestHandler<GetSessionsByContextQuery, OperationSessionListDto>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<GetSessionsByContextHandler> _logger;

    public GetSessionsByContextHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<GetSessionsByContextHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<OperationSessionListDto> Handle(GetSessionsByContextQuery request, CancellationToken cancellationToken)
    {
        var sessions = await _sessionRepository.GetSessionsByContextAsync(
            request.Area, 
            request.ContextId, 
            request.OnlyActive, 
            cancellationToken);
        
        var sessionDtos = sessions.Select(MapToDto).ToList();

        return new OperationSessionListDto
        {
            Sessions = sessionDtos,
            TotalCount = sessionDtos.Count,
            ActiveCount = sessionDtos.Count(s => s.IsActive)
        };
    }

    private static OperationSessionDto MapToDto(OperationSession session)
    {
        return new OperationSessionDto
        {
            Id = session.Id,
            Area = session.Area,
            ContextId = session.ContextId,
            PracownikId = session.PracownikId,
            DeviceId = session.DeviceId,
            StartedAt = session.StartedAt,
            EndedAt = session.EndedAt,
            IsActive = session.IsActive,
            SystemId = session.SystemId,
            CurrentOperation = session.CurrentOperation,
            PracownikImieNazwisko = session.Pracownik?.ImieNazwisko,
            ContextDescription = session.GetContextDescription(),
            Duration = session.Duration,
            IsExpired = session.IsExpired
        };
    }
}

/// <summary>
/// Handler sprawdzania czy istnieje aktywna sesja
/// </summary>
public class HasActiveSessionHandler : IRequestHandler<HasActiveSessionQuery, bool>
{
    private readonly IOperationSessionRepository _sessionRepository;
    private readonly ILogger<HasActiveSessionHandler> _logger;

    public HasActiveSessionHandler(
        IOperationSessionRepository sessionRepository,
        ILogger<HasActiveSessionHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(HasActiveSessionQuery request, CancellationToken cancellationToken)
    {
        if (request.PracownikId.HasValue && request.Area.HasValue)
        {
            return await _sessionRepository.HasActiveSessionAsync(
                request.PracownikId.Value, 
                request.Area.Value, 
                cancellationToken);
        }
        
        if (request.Area.HasValue && request.ContextId.HasValue)
        {
            return await _sessionRepository.HasActiveSessionAsync(
                request.Area.Value, 
                request.ContextId.Value, 
                cancellationToken);
        }

        return false;
    }
}

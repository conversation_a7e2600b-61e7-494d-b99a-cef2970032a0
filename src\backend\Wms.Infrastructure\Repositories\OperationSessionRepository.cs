using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

/// <summary>
/// Implementacja repozytorium dla uniwersalnych sesji operacyjnych
/// </summary>
public class OperationSessionRepository : IOperationSessionRepository
{
    private readonly WmsDbContext _context;
    private readonly ILogger<OperationSessionRepository> _logger;

    public OperationSessionRepository(WmsDbContext context, ILogger<OperationSessionRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    // Read operations (AsNoTracking)

    public async Task<OperationSession?> GetByIdAsync(int sessionId, CancellationToken cancellationToken = default)
    {
        return await _context.OperationSessions
            .AsNoTracking()
            .Include(s => s.Pracownik)
            .FirstOrDefaultAsync(s => s.Id == sessionId, cancellationToken);
    }

    public async Task<OperationSession?> GetActiveSessionAsync(
        int pracownikId, 
        OperationArea area, 
        CancellationToken cancellationToken = default)
    {
        return await _context.OperationSessions
            .AsNoTracking()
            .Include(s => s.Pracownik)
            .FirstOrDefaultAsync(s => 
                s.PracownikId == pracownikId && 
                s.Area == area && 
                s.IsActive && 
                !s.EndedAt.HasValue, 
                cancellationToken);
    }

    public async Task<OperationSession?> GetActiveSessionAsync(
        int pracownikId, 
        OperationArea area, 
        int contextId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.OperationSessions
            .AsNoTracking()
            .Include(s => s.Pracownik)
            .FirstOrDefaultAsync(s => 
                s.PracownikId == pracownikId && 
                s.Area == area && 
                s.ContextId == contextId &&
                s.IsActive && 
                !s.EndedAt.HasValue, 
                cancellationToken);
    }

    public async Task<IEnumerable<OperationSession>> GetActiveSessionsAsync(
        int pracownikId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.OperationSessions
            .AsNoTracking()
            .Include(s => s.Pracownik)
            .Where(s => s.PracownikId == pracownikId && s.IsActive && !s.EndedAt.HasValue)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OperationSession>> GetSessionsByContextAsync(
        OperationArea area, 
        int contextId, 
        bool onlyActive = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.OperationSessions
            .AsNoTracking()
            .Include(s => s.Pracownik)
            .Where(s => s.Area == area && s.ContextId == contextId);

        if (onlyActive)
        {
            query = query.Where(s => s.IsActive && !s.EndedAt.HasValue);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<bool> HasActiveSessionAsync(
        OperationArea area, 
        int contextId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.OperationSessions
            .AsNoTracking()
            .AnyAsync(s => 
                s.Area == area && 
                s.ContextId == contextId && 
                s.IsActive && 
                !s.EndedAt.HasValue, 
                cancellationToken);
    }

    public async Task<bool> HasActiveSessionAsync(
        int pracownikId, 
        OperationArea area, 
        CancellationToken cancellationToken = default)
    {
        return await _context.OperationSessions
            .AsNoTracking()
            .AnyAsync(s => 
                s.PracownikId == pracownikId && 
                s.Area == area && 
                s.IsActive && 
                !s.EndedAt.HasValue, 
                cancellationToken);
    }

    // Write operations

    public async Task<OperationSession> CreateSessionAsync(
        OperationArea area,
        int contextId,
        int pracownikId,
        string deviceId,
        string? currentOperation = null,
        int? systemId = 1,
        CancellationToken cancellationToken = default)
    {
        var session = new OperationSession
        {
            Area = area,
            ContextId = contextId,
            PracownikId = pracownikId,
            DeviceId = deviceId,
            StartedAt = DateTime.UtcNow,
            IsActive = true,
            SystemId = systemId,
            CurrentOperation = currentOperation
        };

        _context.OperationSessions.Add(session);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Created operation session {SessionId} for user {UserId} in area {Area} with context {ContextId}",
            session.Id, pracownikId, area, contextId);

        return session;
    }

    public async Task<bool> EndSessionAsync(
        int sessionId, 
        int pracownikId, 
        CancellationToken cancellationToken = default)
    {
        var session = await _context.OperationSessions
            .FirstOrDefaultAsync(s => 
                s.Id == sessionId && 
                s.PracownikId == pracownikId && 
                s.IsActive, 
                cancellationToken);

        if (session == null)
        {
            _logger.LogWarning("Session {SessionId} not found or not active for user {UserId}", 
                sessionId, pracownikId);
            return false;
        }

        session.EndSession();
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Ended operation session {SessionId} for user {UserId} in area {Area}",
            sessionId, pracownikId, session.Area);

        return true;
    }

    public async Task<int> EndActiveSessionsAsync(
        int pracownikId, 
        OperationArea area, 
        CancellationToken cancellationToken = default)
    {
        var sessions = await _context.OperationSessions
            .Where(s => 
                s.PracownikId == pracownikId && 
                s.Area == area && 
                s.IsActive && 
                !s.EndedAt.HasValue)
            .ToListAsync(cancellationToken);

        foreach (var session in sessions)
        {
            session.EndSession();
        }

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Ended {Count} active sessions for user {UserId} in area {Area}",
            sessions.Count, pracownikId, area);

        return sessions.Count;
    }

    public async Task<int> EndSessionsByContextAsync(
        OperationArea area, 
        int contextId, 
        CancellationToken cancellationToken = default)
    {
        var sessions = await _context.OperationSessions
            .Where(s => 
                s.Area == area && 
                s.ContextId == contextId && 
                s.IsActive && 
                !s.EndedAt.HasValue)
            .ToListAsync(cancellationToken);

        foreach (var session in sessions)
        {
            session.EndSession();
        }

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Ended {Count} active sessions for area {Area} context {ContextId}",
            sessions.Count, area, contextId);

        return sessions.Count;
    }

    public async Task<bool> UpdateCurrentOperationAsync(
        int sessionId, 
        string? currentOperation, 
        CancellationToken cancellationToken = default)
    {
        var session = await _context.OperationSessions
            .FirstOrDefaultAsync(s => s.Id == sessionId && s.IsActive, cancellationToken);

        if (session == null)
            return false;

        session.CurrentOperation = currentOperation;
        await _context.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<int> EndExpiredSessionsAsync(CancellationToken cancellationToken = default)
    {
        var expiredSessions = await _context.OperationSessions
            .Where(s => 
                s.IsActive && 
                !s.EndedAt.HasValue && 
                s.StartedAt < DateTime.UtcNow.AddHours(-8))
            .ToListAsync(cancellationToken);

        foreach (var session in expiredSessions)
        {
            session.EndSession();
        }

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Ended {Count} expired sessions", expiredSessions.Count);

        return expiredSessions.Count;
    }
}
